const REQUEST = require('../utils/request.js')

//获取聊天记录
function getMessageRecord(userId){
	return REQUEST.request('/users-api/v1/private-message/record?userId='+userId, 'GET', {}, true)
}

//获取站内私信列表
function getPrivateMessageList(){
	return REQUEST.request('/users-api/v1/private-message/list', 'GET', {}, true)
}

//分页查询系统该消息
function getSystemMessageList(params){
	return REQUEST.request('/users-api/v1/system-message/page', 'GET', params, true)
}

module.exports = {
	getMessageRecord,
	getPrivateMessageList,
	getSystemMessageList
}