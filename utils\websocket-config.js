/**
 * WebSocket配置文件
 * 集中管理WebSocket相关的配置参数
 */

const WebSocketConfig = {
  // 服务器配置
  server: {
    // WebSocket服务器地址（不包含token）
    baseUrl: 'ws://10.37.13.5:8080/websocket/',

    // 支持的协议
    protocols: ['chat'],

    // 连接超时时间(ms)
    timeout: 10000
  },

  // 重连配置
  reconnect: {
    // 最大重连次数
    maxAttempts: 5,
    
    // 基础延迟时间(ms)
    baseDelay: 1000,
    
    // 最大延迟时间(ms)
    maxDelay: 30000,
    
    // 是否启用指数退避算法
    useExponentialBackoff: true
  },

  // 心跳配置
  heartbeat: {
    // 心跳间隔(ms)
    interval: 30000,
    
    // 心跳超时时间(ms)
    timeout: 5000,
    
    // 最大允许连续丢失心跳次数
    maxMissed: 3,
    
    // 心跳消息格式
    message: {
      topic: 'ping',
      message: {}
    }
  },

  // 消息配置
  message: {
    // 消息队列最大长度
    maxQueueLength: 100,
    
    // 消息重试次数
    maxRetries: 3,
    
    // 消息超时时间(ms)
    timeout: 10000,
    
    // 支持的消息类型
    supportedTypes: ['text', 'image', 'file', 'system'],
    
    // 消息主题定义
    topics: {
      // 发送私聊消息
      SEND_PRIVATE_MESSAGE: 'send_private_message',

      // 私聊消息响应
      SEND_PRIVATE_MESSAGE_RESPONSE: 'send_private_message_response',

      // 心跳发送
      PING: 'ping',

      // 心跳响应
      PONG: 'pong',

      // 系统通知
      SYSTEM_NOTIFICATION: 'system_notification',

      // 用户状态
      USER_STATUS: 'user_status',

      // 群组消息
      GROUP_MESSAGE: 'group_message'
    }
  },

  // 日志配置
  logging: {
    // 是否启用详细日志
    verbose: true,

    // 日志级别: 'debug', 'info', 'warn', 'error'
    level: 'info',

    // 是否记录消息内容
    logMessageContent: true
  },

  // 安全配置
  security: {
    // 是否验证消息签名
    verifySignature: false,

    // 是否加密消息内容
    encryptMessage: false
  },

  // 性能配置
  performance: {
    // 是否启用消息压缩
    enableCompression: false,
    
    // 批量发送消息的间隔(ms)
    batchSendInterval: 100,
    
    // 最大批量发送消息数量
    maxBatchSize: 10
  },



  /**
   * 验证配置的有效性
   * @param {Object} config - 要验证的配置
   * @returns {Object} 验证结果 { valid: boolean, errors: string[] }
   */
  validateConfig: function(config) {
    const errors = [];
    
    // 验证服务器配置
    if (!config.server || !config.server.baseUrl) {
      errors.push('服务器地址不能为空');
    }
    
    if (config.server && config.server.timeout && config.server.timeout < 1000) {
      errors.push('连接超时时间不能少于1秒');
    }
    
    // 验证重连配置
    if (config.reconnect && config.reconnect.maxAttempts < 0) {
      errors.push('最大重连次数不能为负数');
    }
    
    // 验证心跳配置
    if (config.heartbeat && config.heartbeat.interval < 5000) {
      errors.push('心跳间隔不能少于5秒');
    }
    
    return {
      valid: errors.length === 0,
      errors: errors
    };
  }
};

module.exports = WebSocketConfig;
