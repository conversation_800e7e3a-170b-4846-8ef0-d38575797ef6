/**
 * 底部导航栏管理工具
 * 统一管理 tabbar 的状态设置，确保角标数据不会丢失
 */

const messageApi = require('../api/messageApi.js')
const util = require('./util.js')

const TabbarManager = {
  // 缓存的未读消息数量
  cachedUnreadCount: 0,

  /**
   * 设置 tabbar 选中状态
   * @param {Object} page - 页面实例
   * @param {number} selectedIndex - 选中的 tab 索引
   * @param {Object} options - 额外选项
   */
  setTabbarSelected: function(page, selectedIndex, options = {}) {
    if (typeof page.getTabBar === 'function' && page.getTabBar()) {
      const tabbarData = {
        selected: selectedIndex,
        totalUnreadCount: this.cachedUnreadCount
      };

      // 合并额外的选项
      Object.assign(tabbarData, options);

      page.getTabBar().setData(tabbarData);
    }
  },

  /**
   * 更新未读消息数量
   * @param {number} count - 未读消息数量
   */
  updateUnreadCount: function(count) {
    this.cachedUnreadCount = count;
    
    // 通知所有页面更新 tabbar
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      if (typeof currentPage.getTabBar === 'function' && currentPage.getTabBar()) {
        currentPage.getTabBar().setData({
          totalUnreadCount: count
        });
      }
    }
  },

  /**
   * 获取总未读消息数量
   * @returns {Promise} 返回未读消息数量
   */
  getTotalUnreadCount: function() {
    return new Promise((resolve, reject) => {
      const selectedCommunity = wx.getStorageSync('selectedCommunity');
      if (!selectedCommunity || !selectedCommunity.id) {
        console.log('TabbarManager: 未选择社区，无法获取未读消息数量');
        resolve(0);
        return;
      }

      // 检查是否已认证
      if (!util.checkAuthentication()) {
        console.log('TabbarManager: 用户未认证，无法获取未读消息数量');
        resolve(0);
        return;
      }

      const params = {
        pageNum: 1,
        pageSize: 50,
        types: [], // 空数组表示获取所有类型的总数
        communityId: selectedCommunity.id
      };

      console.log('TabbarManager: 获取总未读消息数量参数：', params);

      messageApi.getUnReadCount(params).then(res => {
        console.log('TabbarManager: 总未读消息数量响应：', res);
        
        if (res) {
          const totalCount = res.total || 0;
          this.updateUnreadCount(totalCount);
          resolve(totalCount);
        } else {
          resolve(0);
        }
      }).catch(err => {
        console.error('TabbarManager: 获取总未读消息数量失败：', err);
        resolve(0);
      });
    });
  },

  /**
   * 初始化 tabbar 管理器
   * 在 app.js 中调用，确保应用启动时初始化
   */
  init: function() {
    console.log('TabbarManager: 初始化');
    // 延迟获取未读消息数量，等待登录完成
    setTimeout(() => {
      this.getTotalUnreadCount();
    }, 2000);
  }
};

module.exports = TabbarManager;
