// index.js
const util = require('../../utils/util.js')
const app = getApp()
const PointsUtil = require('../../utils/points')
const PointsService = require('../../services/points')
const imagetextApi = require('../../api/imagetext.js')
const { getAllDict } = require('../../api/commApi.js')
const noticeApi = require('../../api/notice.js')
const commApi = require('../../api/commApi.js')
const TabbarManager = require('../../utils/tabbar-manager.js')
const userApi = require('@/api/userApi.js');
Page({
  data: {
    apiUrl: wx.getStorageSync('apiUrl'),
    isAuthenticated: false,
    userName: '',
    selectedCommunity: '',
    greeting: '',
    weather: {
      temperature: '23°C',
      condition: 'sunny'
    },
    weatherMood: '',
    showAuthModal: false, // 控制物业管理认证弹窗显示
    account: '', // 物业管理账号
    password: '', // 物业管理密码
    rememberAccount: false, // 是否记住账号密码
    showPassword: false, // 是否显示密码
    currentTab: 'property',
    darkMode: false,
    // 轮播图数据
    bannerList: [],
    bannerLoading: false, // 修改默认值为false，避免重复加载
    bannerLoaded: false, // 添加加载状态标记
    // 默认轮播图数据（备用）

    // 积分相关数据
    userPoints: 0,
    userLevel: {
      id: 1,
      name: '普通会员',
      minPoints: 0,
      maxPoints: 999
    },
    nextLevel: {
      id: 2,
      name: '银卡会员',
      minPoints: 1000,
      maxPoints: 4999
    },
    pointsToNextLevel: 1000,
    levelProgress: 0,
    todayVisitorsCount: 0, // 今日访客数量
    unreadCounts: {
      property: 2,
      service: 1,
      community: 0,
      emergency: 1
    },
    messages: {},
    events: [
      {
        id: 1,
        title: '中秋团圆晚会',
        description: '共度中秋佳节，邻里同欢。活动包括猜灯谜、品月饼、文艺表演等多种形式，欢迎社区居民踊跃参与。',
        date: '09-29',
        participants: 42,
        image: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
      },
      {
        id: 2,
        title: '社区健康讲座',
        description: '邀请三甲医院专家主讲，内容包括常见疾病预防和健康生活方式，适合所有年龄段居民参加。',
        date: '10-05',
        participants: 28,
        image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
      },
      {
        id: 3,
        title: '亲子运动会',
        description: '为增进亲子关系，特举办此次运动会。活动包括亲子接力、趣味游戏等多个环节，适合3-12岁儿童及其家长参加。',
        date: '10-12',
        participants: 35,
        image: 'https://images.unsplash.com/photo-*************-4698179dc6ce?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60'
      }
    ],
    showMessageModal: false,
    currentMessage: {},
    currentMessageIcon: 'icon-property',
    // 未读消息总数
    totalUnreadCount: 0
  },

  onLoad: function () {
    this.initPage()
    // this.loadSavedAccount()

    // 等待登录完成后再加载需要token的数据
    this.waitForLoginAndLoadData()

    // 初始化积分服务
    PointsService.init()
  },

  onShow: function () {

     // 检查小区选择
     this.checkCommunitySelection()


  },

  // 等待登录完成后加载数据
  waitForLoginAndLoadData: function () {

    // 如果已经在加载中或已加载，避免重复请求
    if (this.data.bannerLoading || this.data.bannerLoaded) {

      return;
    }

    // 添加超时机制，防止无限等待
    const loginTimeout = new Promise((resolve, reject) => {
      setTimeout(() => {
        reject(new Error('登录超时'));
      }, 10000); // 10秒超时
    });

    // 等待应用登录初始化完成，带超时保护
    Promise.race([app.waitForLogin(), loginTimeout])
      .then(() => {
        // 登录完成后加载需要token的数据
   
        //所有的字典
        this.getAllDict()
        //轮播图数据
        this.loadBannerList();

        // 检查认证状态
        this.checkAuthStatus()

        // 获取总未读消息数量 - 避免重复请求，只在必要时调用
        if (!this.data.totalUnreadCount || this.data.totalUnreadCount === 0) {
          this.loadTotalUnreadCount()
        }

        // 获取今日访客数量
        this.getTodayVisitorsCount()

        // 加载用户积分和等级信息
        this.loadUserPointsInfo()


        // 检查是否有积分变动事件
        if (app.globalData && app.globalData.pointsChangeEvent) {

          // 重新加载用户积分和等级信息
          this.loadUserPointsInfo();
        }

        // 更新底部tabbar选中状态
        TabbarManager.setTabbarSelected(this, 0)

        // 避免重复加载轮播图 - 只在首次进入或数据为空时加载
        if (!this.data.bannerList || this.data.bannerList.length === 0) {

          // 添加标记避免重复加载
          if (!this.data.bannerLoading) {
            this.waitForLoginAndLoadData();
          }
        }


      })
      .catch((error) => {
        console.error('首页: 登录失败或超时，使用默认数据', error);
        // 登录失败或超时时使用默认数据
        this.setData({
          bannerList: [],
          bannerLoading: false,
          bannerLoaded: true
        });

        // 如果是超时，尝试直接加载数据（可能是网络问题）
        if (error.message === '登录超时') {

          setTimeout(() => {
            if (!this.data.bannerLoaded || this.data.bannerList.length === 0) {

              // 重置状态，允许重新加载
              this.setData({
                bannerLoading: false,
                bannerLoaded: false
              });
              this.loadBannerList();
            }
          }, 2000);
        }
      });
  },


  //获取物业员工认证信息
  getPropertyStaffInfo() {
    var communityId = wx.getStorageSync('selectedCommunity').id

    if (communityId)
      userApi.getProperTyInfo(communityId).then(res => {

        if (res) {

          // 统一保存用户信息
          wx.setStorageSync('propertyInfo', res);
          wx.setStorageSync('isPropertyStaff', true);

        } else {

          wx.removeStorageSync('propertyInfo')
          wx.setStorageSync('isPropertyStaff', false);

        }
      }).catch(err => {

        console.error('获取物业认证信息失败:', err);
      });
  },
  // 获取今日访客数量
  getTodayVisitorsCount: function () {
    try {
      // 引入访客管理器
      const VisitorManager = require('../../utils/visitor-manager');

      // 获取今日访客数量
      const count = VisitorManager.getTodayVisitorsCount();

      this.setData({
        todayVisitorsCount: count
      });
    } catch (error) {
      console.error('获取今日访客数量失败', error);
    }
  },



  // 加载保存的账号密码
  loadSavedAccount: function () {
    const savedAccount = wx.getStorageSync('propertyAccount');
    const savedPassword = wx.getStorageSync('propertyPassword');
    const rememberAccount = wx.getStorageSync('rememberPropertyAccount');

    if (rememberAccount && savedAccount && savedPassword) {
      this.setData({
        account: savedAccount,
        password: savedPassword,
        rememberAccount: true
      });
    }
  },

  // 切换记住账号密码
  toggleRememberAccount: function () {
    this.setData({
      rememberAccount: !this.data.rememberAccount
    });
  },

  initPage: function () {
    // 初始化问候语和天气
    const greeting = util.getGreeting()
    const weather = util.getWeatherData()
    const weatherMood = util.getWeatherMood(weather.condition)

    this.setData({
      greeting,
      weather,
      weatherMood
    })

  },


  getAllDict() {

    commApi.getAllDict().then(res => {

      wx.setStorageSync('allDict', res.list)

    }).catch(error => {



    });


  },

  // 加载轮播图列表
  loadBannerList: function (retryCount = 0) {
    const maxRetries = 3;

    // 避免重复加载 - 但允许重试时重新加载
    if (this.data.bannerLoading && retryCount === 0) {

      return;
    }

    // 如果已经加载成功且不是重试，跳过
    if (this.data.bannerLoaded && retryCount === 0 && this.data.bannerList.length > 0) {

      return;
    }


    this.setData({ bannerLoading: true })

    // 检查网络状态
    wx.getNetworkType({
      success: (networkRes) => {

        if (networkRes.networkType === 'none') {

          this.setData({
            bannerList: [],
            bannerLoading: false,
            bannerLoaded: true
          });
          return;
        }

        // 有网络连接，继续请求
        this.performBannerRequest(retryCount, maxRetries);
      },
      fail: () => {

        // 获取网络状态失败，继续尝试请求
        this.performBannerRequest(retryCount, maxRetries);
      }
    });
  },

  // 执行轮播图请求
  performBannerRequest: function (retryCount, maxRetries) {


    // 使用带缓存的API获取轮播图
    imagetextApi.getBannerList({
      pageNum: 1,
      pageSize: 10
    }).then(res => {


      if (res && res.list && res.list.length > 0) {
        // 处理轮播图数据
        const bannerList = res.list.map(item => ({
          id: item.id,
          title: item.title || '',
          imageUrl: this.data.apiUrl + '/common-api/v1/file/' + item.imageUrl,
          linkUrl: item.link || '',
          sort: item.sort || 0
        }))

        // 按排序字段排序
        bannerList.sort((a, b) => (a.sort || 0) - (b.sort || 0))


        this.setData({
          bannerList: bannerList,
          bannerLoading: false,
          bannerLoaded: true
        })
      } else {

        // 如果API返回空数据，使用默认轮播图
        this.setData({
          bannerList: [],
          bannerLoading: false,
          bannerLoaded: true
        })
      }
    }).catch(err => {
      console.error(`获取轮播图失败 (尝试 ${retryCount + 1}/${maxRetries + 1}):`, err)
      console.error('错误详情:', {
        message: err.message,
        stack: err.stack,
        name: err.name
      });

      // 重置加载状态，允许重试
      this.setData({ bannerLoading: false });

      // 如果还有重试次数，延迟后重试
      if (retryCount < maxRetries) {
        const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 5000); // 指数退避，最大5秒

        setTimeout(() => {
          this.loadBannerList(retryCount + 1);
        }, retryDelay);
      } else {
        // 重试次数用完，使用默认轮播图


      }
    })
  },

  checkAuthStatus: function () {

    userApi.getUserInfo().then(res => {

      if (res) {
         
        var userInfo = res
        // 统一保存用户信息
        wx.setStorageSync('userInfo', userInfo);

        if (userInfo.role != 'tourist') {

          this.setData({
            isAuthenticated: true
          })

          //获取物业员工认证信息
          this.getPropertyStaffInfo()

        } else {

        }

      } else {

        this.setData({
          isAuthenticated: false,

        })

      }



    }).catch(err => {



    });


  },

  checkCommunitySelection: function (check) {


    const selectedCommunity = wx.getStorageSync('selectedCommunity')

    // 如果存储的是对象（新格式），取name字段
    // 如果存储的是字符串（旧格式），直接使用
    let communityName = ''
    if (selectedCommunity) {
      if (typeof selectedCommunity === 'object' && selectedCommunity.communityName) {
        communityName = selectedCommunity.communityName
      } else if (typeof selectedCommunity === 'string') {
        communityName = selectedCommunity
      }
    }



    this.setData({
      selectedCommunity: communityName
    })


    if (communityName == "") {

      setTimeout(() => {
        this.navigateToCommunitySelect()

      }, 1500);

      return false
    } else {

      return true
    }


  },

  switchTab: function (e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({
      currentTab: tab
    })
  },

  navigateToCommunitySelect: function () {
    wx.navigateTo({
      url: '/pages/community-select/community-select'
    })
  },

  // 社区服务 - 需要登录和选择小区
  navigateToCommunity: function () {
    util.checkStatusAndNavigate('/communityPackage/pages/community/community', {
      requireCommunity: true
    });
  },

  // 缴费服务 - 需要登录和选择小区
  navigateToPayment: function () {
    util.checkStatusAndNavigate('/servicePackage/pages/payment/payment', {
      requireAuth: true,
      requireCommunity: true
    });
  },

  // 消息中心 - 需要登录和选择小区
  navigateToMessages: function () {
    util.checkStatusAndNavigate('/pages/messages/messages', {
      requireCommunity: true
    });
  },

  // 通用的需要认证的页面跳转
  navigateWithAuth: function (e) {
    const url = e.currentTarget.dataset.url;
    util.checkStatusAndNavigate(url, {
      requireAuth: true,
      requireCommunity: true
    });
  },

  // 导航到访客登记页面 - 需要登录、选择小区和认证
  navigateToVisitor: function () {
    util.checkStatusAndNavigate('/servicePackage/pages/visitor/list/index', {
      requireAuth: true,
      requireCommunity: true
    });
  },

  // 导航到报修服务 - 需要登录和选择小区
  navigateToRepair: function () {
    util.checkStatusAndNavigate('/servicePackage/pages/repair/repair', {
      requireCommunity: true
    });
  },

  // 导航到投诉建议 - 需要登录和选择小区
  navigateToComplaint: function () {
    util.checkStatusAndNavigate('/servicePackage/pages/complaint/complaint', {
      requireCommunity: true
    });
  },

  navigateToPropertyManagement: function () {


    util.checkStatusAndNavigate('/propertyPackage/pages/property/property', {
      requireAuth: true,
      requireProperty:true,
      requireCommunity: true
    });

    // // 检查是否已登录
    // const isPropertyStaff = wx.getStorageSync('isPropertyStaff');
    // const loginTime = wx.getStorageSync('propertyLoginTime');
    // const currentTime = new Date().getTime();

    // // 登录状态有效期为24小时
    // const loginValid = isPropertyStaff && loginTime && (currentTime - loginTime < 24 * 60 * 60 * 1000);

    // if (loginValid) {
    //   // 已登录，直接跳转
    //   wx.navigateTo({
    //     url: '/propertyPackage/pages/property/property'
    //   });
    // } else {
    //   // 未登录或登录已过期，显示登录弹窗
    //   this.setData({
    //     showAuthModal: true
    //   });
    // }
  },

  navigateToEventDetail: function (e) {
    const event = e.currentTarget.dataset.event
    if (util.checkAuthentication()) {
      wx.navigateTo({
        url: `/communityPackage/pages/community/event-detail/event-detail?id=${event.id}`
      })
    } else {
      util.showAuthModal()
    }
  },

  // 扫码功能
  scanCode: function () {
    wx.scanCode({
      onlyFromCamera: false,
      scanType: ['qrCode', 'barCode'],
      success: (res) => {

        // 处理扫码结果
        this.handleScanResult(res.result);
      },
      fail: (err) => {
        console.error('扫码失败:', err);
        wx.showToast({
          title: '扫码失败',
          icon: 'none'
        });
      }
    });
  },

  // 处理扫码结果
  handleScanResult: function (result) {
    try {
      // 尝试解析为JSON
      const data = JSON.parse(result);

      if (data.type === 'verification') {
        // 核销码
        this.navigateToVerification(data);
      } else if (data.type === 'activity') {
        // 活动码
        this.navigateToActivity(data);
      } else {
        // 其他类型
        wx.showModal({
          title: '扫码结果',
          content: result,
          showCancel: false
        });
      }
    } catch (e) {
      // 不是JSON格式，可能是URL或其他格式
      if (result.startsWith('http')) {
        wx.showModal({
          title: '是否打开链接?',
          content: result,
          success: (res) => {
            if (res.confirm) {
              wx.navigateTo({
                url: '/pages/webview/webview?url=' + encodeURIComponent(result)
              });
            }
          }
        });
      } else {
        wx.showModal({
          title: '扫码结果',
          content: result,
          showCancel: false
        });
      }
    }
  },

  // 导航到核销页面
  navigateToVerification: function (data) {
    util.checkStatusAndNavigate('/pages/qrCodeScan/qrCodeScan', {
      requireAuth: true,
      requireCommunity: true
    });

  },

  // 导航到活动页面
  navigateToActivity: function (data) {
    if (util.checkAuthentication()) {
      wx.navigateTo({
        url: `/communityPackage/pages/community/event-detail/event-detail?id=${data.id}`
      });
    } else {
      util.showAuthModal(`/communityPackage/pages/community/event-detail/event-detail?id=${data.id}`);
    }
  },

  // 轮播图跳转
  navigateToBanner: function (e) {
    const { url, id } = e.currentTarget.dataset;

    if (!url) {

      return
    }

    // 如果是外部链接（http/https开头）
    if (url.startsWith('http://') || url.startsWith('https://')) {
      // 打开外部链接
      wx.showModal({
        title: '提示',
        content: '即将跳转到外部链接，是否继续？',
        success: (res) => {
          if (res.confirm) {
            // 可以使用webview页面打开外部链接
            wx.navigateTo({
              url: `/pages/webview/webview?url=${encodeURIComponent(url)}`
            });
          }
        }
      });
      return
    }

    // 内部页面跳转
    // 检查是否需要认证
    if (url.includes('/propertyPackage/')) {
      // 物业管理相关页面需要物业认证
      this.navigateToPropertyManagement();
    } else if (util.checkAuthentication()) {
      // 已认证，直接跳转
      wx.navigateTo({
        url: url
      });
    } else {
      // 未认证，显示认证弹窗
      util.showAuthModal(url);
    }
  },

  // 获取总未读消息数量
  loadTotalUnreadCount: function () {
    TabbarManager.getTotalUnreadCount().then(count => {
      this.setData({
        totalUnreadCount: count
      });

    });
  },

  // 加载用户积分和等级信息
  loadUserPointsInfo: function () {
    // 检查是否已认证
    if (!util.checkAuthentication()) {
      return;
    }

    // 使用积分工具获取用户积分和等级信息
    PointsUtil.getUserLevelInfo().then(levelInfo => {
      if (levelInfo) {
        // 设置用户积分和等级信息
        this.setData({
          userPoints: levelInfo.points,
          userLevel: levelInfo.level,
          nextLevel: levelInfo.nextLevel,
          pointsToNextLevel: levelInfo.pointsToNextLevel,
          levelProgress: Math.floor(levelInfo.progress)
        });
      }
    }).catch(err => {
      console.error('获取用户积分和等级信息失败:', err);

      // 如果获取失败，使用默认值
      PointsUtil.getUserPoints().then(points => {
        this.setData({
          userPoints: points
        });
      });
    });
  },



  // 跳转到积分明细页面
  navigateToPointsDetail: function () {
    wx.navigateTo({
      url: '/pages/points/record/record'
    });
  },

  // 跳转到赚积分页面
  navigateToEarnPoints: function () {
    wx.navigateTo({
      url: '/pages/points/earn/earn'
    });
  },

  // 跳转到积分页面
  navigateToPointsMall: function () {
    wx.switchTab({
      url: '/pages/points/points'
    });
  },

  // 跳转到积分规则页面
  navigateToPointsRules: function () {
    wx.navigateTo({
      url: '/pages/points/rules/rules'
    });
  },

  showMessageDetail: function (e) {
    const message = e.currentTarget.dataset.message

    // 标记为已读
    if (!message.read) {
      this.markMessageAsRead(message)
    }

    // 根据消息标题设置图标
    let icon = 'message-repair'
    if (message.title.includes('停水')) {
      icon = 'message-water'
    } else if (message.title.includes('设施')) {
      icon = 'message-facility'
    } else if (message.title.includes('服务')) {
      icon = 'service'
    }

    // 生成消息内容
    let content = this.generateMessageContent(message)

    this.setData({
      showMessageModal: true,
      currentMessage: {
        ...message,
        content
      },
      currentMessageIcon: icon
    })
  },

  closeMessageModal: function () {
    this.setData({
      showMessageModal: false
    })
  },




  stopPropagation: function (e) {
    // 阻止事件冒泡
  },

  markMessageAsRead: function (message) {
    // 更新消息已读状态
    const type = this.data.currentTab
    const messages = this.data.messages[type]
    const unreadCounts = this.data.unreadCounts

    for (let i = 0; i < messages.length; i++) {
      if (messages[i].id === message.id) {
        messages[i].read = true
        break
      }
    }

    // 更新未读数量
    unreadCounts[type] = Math.max(0, unreadCounts[type] - 1)

    this.setData({
      [`messages.${type}`]: messages,
      unreadCounts
    })
  },

  generateMessageContent: function (message) {
    // 根据消息标题生成内容
    if (message.title.includes('电梯维修')) {
      return '尊敬的业主：\n\n因小区1号楼电梯需要进行例行维护保养，电梯将于' +
        (message.time || '今日上午10:00-12:00') + '暂停使用。\n\n维护期间，请您使用其他电梯或楼梯，并听从工作人员指挥。\n\n' +
        '感谢您的理解与配合！'
    } else if (message.title.includes('停水')) {
      return '尊敬的业主：\n\n因市政自来水管道检修需要，小区将于' +
        (message.time || '明日上午9:00-下午3:00') + '实施计划性停水。\n\n请您提前做好储水准备，停水期间如有疑问请拨打物业服务热线。\n\n' +
        '感谢您的理解与配合！'
    } else if (message.title.includes('设施')) {
      return '尊敬的业主：\n\n小区公共健身设施需进行安全检查和维修，将于' +
        (message.time || '近期') + '开始施工。\n\n施工期间该区域将暂时封闭，请您注意安全，不要靠近施工区域。\n\n' +
        '感谢您的理解与配合！'
    } else if (message.title.includes('报修')) {
      return '尊敬的业主：\n\n您提交的报修申请已受理，工作人员将于24小时内与您联系，请保持电话畅通。\n\n' +
        '报修单号：RP' + Math.floor(Math.random() * 10000000) + '\n' +
        '报修时间：' + message.time + '\n\n' +
        '如有疑问，请联系物业客服中心。'
    } else if (message.title.includes('缴纳成功')) {
      return '尊敬的业主：\n\n您的物业费缴纳已成功，感谢您的支持！\n\n' +
        '缴费金额：¥1,280.00\n' +
        '缴费时间：' + message.time + '\n' +
        '缴费周期：2023年07月-2023年12月\n\n' +
        '如有疑问，请联系物业客服中心。'
    } else if (message.title.includes('活动')) {
      return '亲爱的邻居：\n\n我们诚挚地邀请您参加小区即将举办的中秋团圆晚会！\n\n' +
        '活动时间：2023年09月29日 晚上7:00-9:00\n' +
        '活动地点：小区中央花园\n' +
        '活动内容：猜灯谜、品月饼、文艺表演\n\n' +
        '活动现场还将有精美礼品赠送，期待您的参与！'
    } else if (message.title.includes('志愿者')) {
      return '亲爱的邻居：\n\n小区正在招募社区志愿者，参与社区公益活动、邻里互助等工作。\n\n' +
        '招募对象：年满18周岁的小区业主\n' +
        '报名方式：请到物业服务中心填写报名表\n' +
        '报名截止：2023年10月15日\n\n' +
        '期待您的加入，共建美好家园！'
    } else if (message.title.includes('紧急通知')) {
      return '紧急通知！\n\n因电力部门线路检修，小区将于今晚22:00至次日6:00停电。\n\n' +
        '请您提前做好准备：\n' +
        '1. 储备适量饮用水\n' +
        '2. 确保手机等通讯设备电量充足\n' +
        '3. 关闭家中电器电源\n' +
        '4. 注意用火用电安全\n\n' +
        '如有特殊情况，请立即联系物业应急电话：400-123-4567'
    } else {
      return '尊敬的业主：\n\n感谢您一直以来对物业工作的支持与配合。\n\n本则通知为您提供相关服务信息，请您及时查阅并知晓。如有任何疑问，请联系物业服务中心。\n\n' +
        '感谢您的理解与配合！'
    }
  },

  // 关闭物业管理认证弹窗
  closeAuthModal: function () {
    // 如果没有选择记住账号密码，则清空
    if (!this.data.rememberAccount) {
      this.setData({
        showAuthModal: false,
        account: '', // 清空账号
        password: '' // 清空密码
      });
    } else {
      this.setData({
        showAuthModal: false
      });
    }
  },

  // 账号输入监听
  onAccountInput: function (e) {
    this.setData({
      account: e.detail.value
    })
  },

  // 清除账号
  clearAccount: function () {
    this.setData({
      account: ''
    })
  },

  // 密码输入监听
  onPasswordInput: function (e) {
    this.setData({
      password: e.detail.value
    })
  },

  // 切换密码可见性
  togglePasswordVisibility: function () {
    this.setData({
      showPassword: !this.data.showPassword
    })
  },



  // 确认认证
  confirmAuth: function () {
    // 验证账号密码
    if (!this.data.account || !this.data.password) {
      wx.showToast({
        title: '请输入账号和密码',
        icon: 'none'
      });
      return;
    }

    // 直接处理登录
    this.handleLogin();
  },

  // 处理登录
  handleLogin: function () {
    // 显示加载中
    wx.showLoading({
      title: '登录中...',
      mask: true
    });

    // 模拟验证过程
    // 实际应用中，应该将验证结果发送到服务端进行二次验证
    setTimeout(() => {
      wx.hideLoading();

      // 保存登录状态
      wx.setStorageSync('isPropertyStaff', true);
      wx.setStorageSync('propertyLoginTime', new Date().getTime());

      // 如果选择了记住账号密码
      if (this.data.rememberAccount) {
        wx.setStorageSync('propertyAccount', this.data.account);
        wx.setStorageSync('propertyPassword', this.data.password);
        wx.setStorageSync('rememberPropertyAccount', true);
      } else {
        wx.removeStorageSync('propertyAccount');
        wx.removeStorageSync('propertyPassword');
        wx.removeStorageSync('rememberPropertyAccount');
      }

      // 关闭弹窗
      this.closeAuthModal();

      // 导航到物业管理页面，添加动画效果
      wx.showToast({
        title: '登录成功',
        icon: 'success',
        duration: 1000,
        success: () => {
          setTimeout(() => {
            wx.navigateTo({
              url: '/propertyPackage/pages/property/property',
              success: () => {
                // 页面跳转成功后的回调

              }
            });
          }, 500);
        }
      });
    }, 1000);
  },


})
