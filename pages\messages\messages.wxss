/* messages.wxss */
.container {
  padding: 30rpx;
}

.page-header {
  margin-bottom: 40rpx;
}

.page-title {
  font-size: 44rpx;
  font-weight: 600;
  color: #333;
}

/* 消息中心 */
/* 主要tab标签容器 */
.main-tabs-wrapper {
  background: white;
  border-bottom: 2rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.main-tabs {
  display: flex;
  padding: 0 30rpx;
}

.main-tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 20rpx;
  font-size: 32rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.main-tab.active {
  color: #ff8c00;
  border-bottom-color: #ff8c00;
  font-weight: 600;
}

.tab-text {
  position: relative;
}

/* 未读数量角标 */
.unread-badge {
  position: absolute;
  top: -10rpx;
  right: -20rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
  box-sizing: border-box;
}

.unread-count {
  color: white;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1;
  transform: scale(0.9);
}

/* 子分类标签容器 */
.sub-tabs-wrapper {
  position: relative;
  background: white;
  border: 2rpx solid #E3F2FD;
  border-radius: 16rpx;
  margin-bottom: 32rpx;
  overflow: hidden;
}

/* 标签加载状态 */
.tabs-loading {
  padding: 40rpx 20rpx;
  text-align: center;
}

.tabs-loading .loading-text {
  font-size: 28rpx;
  color: #999;
}

.sub-tabs-container {
  background: #F8F8F8;
  border-radius: 20rpx;
  padding: 8rpx;
  white-space: nowrap;
}

.sub-tabs {
  display: flex;
  padding: 8rpx;
}

.sub-tab {
  flex-shrink: 0;
  padding: 16rpx 32rpx;
  margin-right: 16rpx;
  background: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #666;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.sub-tab.active {
  background: #ff8c00;
  color: white;
  font-weight: 600;
}

.sub-tab:last-child {
  margin-right: 0;
}

.message-tab {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
  position: relative;
  display: flex;
  align-items: center;
}

.message-tab.active {
  color: #FF6B00;
  font-weight: 600;
  background: #fff;
  border-radius: 8rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab-badge {
  background: #FF3B30;
  color: white;
  font-size: 22rpx;
  min-width: 36rpx;
  height: 36rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12rpx;
  padding: 0 8rpx;
}

/* 滚动提示图标 */
.scroll-hint {
  position: absolute;
  right: 0rpx;
  top: 45%;
  transform: translateY(-50%);
  width: 20rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.scroll-hint-icon {
  color: #007AFF;
  font-size: 64rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(0deg);
}

/* 脉冲动画 */
@keyframes pulse {
  0% {
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
    transform: translateY(-50%) scale(1);
  }
  50% {
    box-shadow: 0 6rpx 20rpx rgba(0, 122, 255, 0.5);
    transform: translateY(-50%) scale(1.05);
  }
  100% {
    box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
    transform: translateY(-50%) scale(1);
  }
}

.message-panel {
  display: none;
}

.message-panel.active {
  display: block;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid rgba(60, 60, 67, 0.1);
}

.message-item:last-child {
  border-bottom: none;
}

.message-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24rpx;
  color: #007AFF;
  background: rgba(245, 245, 247, 0.8);
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.02);
}

.message-info {
  flex: 1;
}

.message-title {
  font-size: 32rpx;
  margin-bottom: 8rpx;
  color: #1C1C1E;
  font-weight: 500;
}

.message-time {
  font-size: 26rpx;
  color: #8E8E93;
}

.message-status {
  width: 20rpx;
  height: 20rpx;
  border-radius: 10rpx;
  margin-left: 16rpx;
}

.message-status.unread {
  background: #FF3B30;
}

.message-item.emergency {
  background-color: rgba(255, 59, 48, 0.05);
  border-left: 6rpx solid #FF3B30;
  padding-left: 24rpx;
  border-radius: 8rpx;
}

/* 消息加载状态 */
.messages-loading {
  padding: 80rpx 20rpx;
  text-align: center;
}

.messages-loading .loading-text {
  font-size: 28rpx;
  color: #999;
}

/* 空消息状态 */
.empty-messages {
  padding: 120rpx 20rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 消息详情弹窗 */
.message-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.35s cubic-bezier(0.33, 1, 0.68, 1);
  backdrop-filter: blur(10px);
}

.message-detail-modal.show {
  opacity: 1;
  visibility: visible;
}

.message-detail-content {
  width: 85%;
  max-width: 640rpx;
  background-color: white;
  border-radius: 32rpx;
  overflow: hidden;
  transform: scale(0.9) translateY(40rpx);
  transition: transform 0.35s cubic-bezier(0.33, 1, 0.68, 1);
  box-shadow: 0 24rpx 60rpx rgba(0, 0, 0, 0.15);
}

.message-detail-modal.show .message-detail-content {
  transform: scale(1) translateY(0);
}

.message-detail-header {
  padding: 40rpx 48rpx 0;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  padding-bottom: 32rpx;
}

.message-detail-icon {
  width: 84rpx;
  height: 84rpx;
  border-radius: 50%;
  background-color: rgba(0, 122, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.message-detail-title-container {
  flex: 1;
}

.message-detail-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #000;
  margin-bottom: 8rpx;
}

.message-detail-time {
  font-size: 26rpx;
  color: #8E8E93;
}

.message-detail-body {
  padding: 32rpx 48rpx;
  max-height: 600rpx;
  overflow-y: auto;
}

.message-detail-text {
  font-size: 30rpx;
  line-height: 1.6;
  color: #3A3A3C;
  white-space: pre-line;
}

.message-detail-footer {
  display: flex;
  border-top: 1rpx solid rgba(0, 0, 0, 0.08);
}

.message-detail-close-btn {
  flex: 1;
  height: 108rpx;
  border: none;
  background: none;
  font-size: 32rpx;
  font-weight: 500;
  color: #007AFF;
}

.message-detail-close-btn:active {
  background-color: #e6e6e6;
}

/* 站内私信样式 */
.message-item.private-message {
  padding: 24rpx 30rpx;
}

.message-avatar {
  width: 80rpx;
  height: 80rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #f0f0f0;
}

.message-content-preview {
  font-size: 26rpx;
  color: #999;
  margin-top: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-type-badge {
  background: #e3f2fd;
  color: #1976d2;
  font-size: 22rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  align-self: flex-start;
  margin-top: 8rpx;
}

/* 加载更多和没有更多数据的样式 */
.load-more, .no-more {
  text-align: center;
  padding: 40rpx 20rpx;
}

.load-more-text, .no-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 私信未读数量显示 */
.message-unread-count {
  min-width: 40rpx;
  height: 40rpx;
  background: #ff4757;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 12rpx;
  margin-left: 16rpx;
}

.unread-count-text {
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 1;
}
