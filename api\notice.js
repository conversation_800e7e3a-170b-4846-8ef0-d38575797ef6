
const REQUEST = require('@/utils/request.js')

//分页查询消息
function getNoticePage(params){
	return REQUEST.request('/users-api/v1/notice/page', 'GET',params, true)
}

//获取消息详情
function getNoticeById(id)
{
	return REQUEST.request('/users-api/v1/notice?id='+id, 'GET',{}, true)
}


//消息已读
function setNoticeRead(id)
{
	return REQUEST.request('/users-api/v1/notice/read?id='+id, 'POST',{}, true)
}


module.exports={
	getNoticePage,
	getNoticeById,
	setNoticeRead
	
}