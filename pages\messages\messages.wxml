<!--messages.wxml-->
<view class="container page-bottom-safe-area">

  <!-- 主要tab标签 -->
  <view class="main-tabs-wrapper">
    <view class="main-tabs">
      <view class="main-tab {{currentMainTab === item.key ? 'active' : ''}}"
            wx:for="{{mainTabs}}"
            wx:key="key"
            bindtap="switchMainTab"
            data-tab="{{item.key}}">
        {{item.name}}
      </view>
    </view>
  </view>



  <!-- 消息内容区 -->
  <view class="message-content list-container">
    <!-- 消息加载状态 -->
    <view class="messages-loading" wx:if="{{messagesLoading}}">
      <view class="loading-text">正在加载消息...</view>
    </view>

    <!-- 消息列表 -->
    <view class="message-panel active" wx:if="{{!messagesLoading}}">
      <!-- 有消息时显示列表 -->
      <view wx:if="{{messages && messages.length > 0}}">
        <!-- 系统消息 -->
        <view class="message-item"
              wx:if="{{currentMainTab === 'system_message'}}"
              wx:for="{{messages}}"
              wx:key="id"
              bindtap="showMessageDetail"
              data-message="{{item}}">
          <view class="message-icon">
            <icon class="iconfont icon-system"></icon>
          </view>
          <view class="message-info">
            <view class="message-title">{{item.title}}</view>
            <view class="message-time">{{item.time}}</view>
          </view>
        </view>

        <!-- 通知公告 -->
        <view class="message-item {{item.type === 'emergency_notice' ? 'emergency' : ''}}"
              wx:if="{{currentMainTab === 'notice_announcement'}}"
              wx:for="{{messages}}"
              wx:key="id"
              bindtap="showMessageDetail"
              data-message="{{item}}">
          <view class="message-icon">
            <icon class="iconfont {{item.type === 'property_notice' ? 'icon-property' : item.type === 'customer_message' ? 'icon-service' : item.type === 'community_notice' ? 'icon-community' : item.type === 'emergency_notice' ? 'icon-emergency' : 'icon-property'}}"></icon>
          </view>
          <view class="message-info">
            <view class="message-title">{{item.title}}</view>
            <view class="message-time">{{item.time}}</view>
          </view>
          <!-- 未读状态红点指示器 - 只在未读时显示 -->
          <view class="message-status unread" wx:if="{{!item.read}}"></view>
        </view>

        <!-- 站内私信 -->
        <view class="message-item private-message"
              wx:if="{{currentMainTab === 'private_message'}}"
              wx:for="{{messages}}"
              wx:key="id"
              bindtap="showMessageDetail"
              data-message="{{item}}">
          <view class="message-avatar">
            <image class="avatar-img" src="{{item.avatarUrl ? (apiUrl + item.avatarUrl) : '/images/default-avatar.svg'}}" mode="aspectFill"></image>
          </view>
          <view class="message-info">
            <view class="message-title">{{item.userName}}</view>
            <view class="message-content-preview">{{item.type=='text'?item.content:'[图片]'}}</view>
            <view class="message-time">{{item.time}}</view>
          </view>
          <!-- <view class="message-type-badge">{{item.type === 'text' ? '文本' : '图片'}}</view> -->
        </view>
      </view>

      <!-- 无消息时显示空状态 -->
      <view class="empty-messages" wx:else>
        <view class="empty-icon">📭</view>
        <view class="empty-text">暂无消息</view>
      </view>

      <!-- 加载更多提示 -->
      <view class="load-more" wx:if="{{hasMore && messages.length > 0}}">
        <view class="load-more-text">上拉加载更多</view>
      </view>

      <!-- 没有更多数据提示 -->
      <view class="no-more" wx:if="{{!hasMore && messages.length > 0}}">
        <view class="no-more-text">没有更多数据了</view>
      </view>
    </view>
  </view>
</view>

<!-- 消息详情弹窗 -->
<view class="message-detail-modal {{showMessageModal ? 'show' : ''}}" bindtap="closeMessageModal">
  <view class="message-detail-content" catchtap="stopPropagation">
    <view class="message-detail-header">
      <view class="message-detail-icon">
        <icon class="iconfont {{currentMessageIcon}}"></icon>
      </view>
      <view class="message-detail-title-container">
        <view class="message-detail-title">{{currentMessage.title}}</view>
        <view class="message-detail-time">{{currentMessage.time}}</view>
      </view>
    </view>
    <view class="message-detail-body">
      <view class="message-detail-text">{{currentMessage.content}}</view>
    </view>
    <view class="message-detail-footer">
      <button class="message-detail-close-btn" bindtap="closeMessageModal">我知道了</button>
    </view>
  </view>
</view>
